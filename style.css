* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
  font-family: Arial;
  border: 1px solid black;
}

#logo-div {
  width: 100%;
  min-height: 50px;
  /* background-color: lightgray; */
  padding-left: 2%;
  line-height: 50px;
  margin-bottom: 10px;
}

#nav-div {
  width: 100%;
  min-height: 30px;
  /* background-color: lightgray; */
  text-align: center;
  line-height: 30px;
  margin-bottom: 10px;
}

#header-banner-div {
  width: 100%;
  min-height: 100px;
  /* background-color: lightgray; */
  /* text-align: center; */
  /* line-height: 100px; */
  margin-bottom: 10px;
}

#main-div {
  width: 100%;
  min-height: 400px;
  /* background-color: gray; */
  margin-bottom: 10px;
}

#sidebar-div {
  width: 20%;
  min-height: 400px;
  /* background-color: lightgray; */
  float: left;
  text-align: center;
  /* line-height: 400px; */
}

#bodyarea-div {
  width: 77%;
  min-height: 400px;
  /* background-color: lightgray; */
  float: right;
  /* text-align: center; */
  /* line-height: 400px; */
}

.clearfix::after {
  content: "";
  display: block;
  clear: both;
}

#footer-div {
  width: 100%;
  min-height: 50px;
  /* background-color: lightgray; */
  text-align: center;
  line-height: 50px;
}

#wrapper-div {
  width: 80%;
  background-color: green;
  margin: auto;
}

.dice-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: rgb(119, 175, 119);
  padding: 1rem;
}

#scoreboard-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  background-color: lightgray;
  /* display: inline-block; */
}

#scoreboard {
  display: flex;
  flex-direction: column;
  width: 20%;
  height: 100%;
  background-color: lightgray;
  /* display: inline-block; */
}

.scoreboard-left {
  width: 100%;
  height: 100%;
  /* float: left; */
}

.scoreboard-right {
  width: 100%;
  height: 100%;
  background-color: blue;
  /* display: inline-block; */
  /* float: left; */
}

.dice {
  /* display: none; */
  /* margin-left: 35%; */
  display: inline-block;
  width: 100px;
  height: 100px;
  line-height: 100px;
  /* padding: 20px; */
  border: 1px solid black;
  /* background-color: aqua; */
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 0 20px black;
  text-align: center;
  margin: 10px;
}

.dice:nth-child(2) {
  /* margin-left: 40%; */
  /* margin-right: 5%; */
}

#diceScore {
  text-align: center;
}

#startGameBtn {
  background-color: rgb(210, 197, 221);
  width: 150px;
  height: 35px;
}

#nextBatterBtn {
  background-color: rgb(93, 253, 93);
  width: 150px;
  height: 35px;
}

table {
  width: auto;
  /* background-color: rgb(159, 231, 255); */
}

table,
th,
td {
  border: 1px solid black;
}

.tblDice {
  text-align: center;
}
