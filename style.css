* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #2c5530 0%, #4a7c59 100%);
  min-height: 100vh;
}

#wrapper-div {
  width: 90%;
  max-width: 1200px;
  background: rgba(255, 255, 255, 0.95);
  margin: 20px auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

#logo-div {
  width: 100%;
  min-height: 60px;
  background: linear-gradient(135deg, #1a4c96 0%, #2563eb 100%);
  color: white;
  padding: 0 20px;
  line-height: 60px;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 0;
}

#nav-div {
  width: 100%;
  min-height: 40px;
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  text-align: center;
  line-height: 40px;
  margin-bottom: 0;
  font-style: italic;
  color: #64748b;
}

#header-banner-div {
  width: 100%;
  min-height: 200px;
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  margin-bottom: 20px;
  padding: 20px;
}

#main-div {
  width: 100%;
  min-height: 400px;
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
  padding: 0 20px;
}

#sidebar-div {
  width: 250px;
  min-height: 400px;
  text-align: center;
  background: #f8fafc;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

#bodyarea-div {
  flex: 1;
  min-height: 400px;
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

#footer-div {
  width: 100%;
  min-height: 50px;
  background: #1f2937;
  color: white;
  text-align: center;
  line-height: 50px;
  font-size: 14px;
}

.dice-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 15px;
  margin-top: 20px;
}

#scoreboard-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

#scoreboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  width: 300px;
  background: rgba(0, 0, 0, 0.1);
  padding: 15px;
  color: white;
  font-weight: bold;
}

.scoreboard-left {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  text-align: center;
  font-size: 14px;
}

#field {
  flex: 1;
  background: linear-gradient(45deg, #22c55e 0%, #16a34a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.dice {
  display: inline-block;
  width: 80px;
  height: 80px;
  line-height: 80px;
  background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%);
  border: 3px solid #334155;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 2px 4px rgba(255, 255, 255, 0.8);
  text-align: center;
  margin: 0 15px;
  font-size: 32px;
  font-weight: bold;
  color: #1e293b;
  transition: all 0.3s ease;
  cursor: pointer;
}

.dice:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.8);
}

#diceScore {
  text-align: center;
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  color: #1e293b;
  font-weight: bold;
  font-size: 16px;
}

#startGameBtn,
#nextBatterBtn {
  width: 180px;
  height: 50px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 10px 0;
}

#startGameBtn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

#startGameBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.6);
}

#nextBatterBtn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

#nextBatterBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.6);
}

table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

th {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  color: white;
  padding: 12px 8px;
  font-weight: bold;
  text-align: center;
  border: none;
}

td {
  padding: 10px 8px;
  border: 1px solid #e2e8f0;
  text-align: center;
}

.tblDice {
  background: #f1f5f9;
  font-weight: bold;
  color: #1e293b;
  width: 60px;
}

.tblOutcome {
  text-align: left;
  padding-left: 12px;
}

tr:nth-child(even) {
  background: #f8fafc;
}

tr:hover {
  background: #e0f2fe;
}

/* Responsive Design */
@media (max-width: 768px) {
  #wrapper-div {
    width: 95%;
    margin: 10px auto;
  }

  #main-div {
    flex-direction: column;
  }

  #sidebar-div {
    width: 100%;
    margin-bottom: 20px;
  }

  #scoreboard-container {
    flex-direction: column;
  }

  #scoreboard {
    width: 100%;
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }

  .dice {
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 24px;
    margin: 0 10px;
  }

  table {
    font-size: 14px;
  }

  th,
  td {
    padding: 8px 4px;
  }
}

/* Additional styling for new elements */
.game-info {
  margin-top: 30px;
  padding: 20px;
  background: #f0f9ff;
  border-radius: 10px;
  border: 2px solid #0ea5e9;
  text-align: left;
}

.game-info h4 {
  color: #0c4a6e;
  margin-bottom: 10px;
  font-size: 16px;
}

.game-info p {
  margin: 5px 0;
  color: #0f172a;
  font-size: 14px;
}

#bodyarea-div h3 {
  color: #1e293b;
  margin-bottom: 15px;
  text-align: center;
  font-size: 18px;
}

#sidebar-div h3 {
  color: #1e293b;
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
}

/* Animation for celebrations */
@keyframes celebrate {
  0% {
    transform: scale(1);
    background: linear-gradient(45deg, #22c55e 0%, #16a34a 100%);
  }
  25% {
    transform: scale(1.05);
    background: linear-gradient(45deg, #fbbf24 0%, #f59e0b 100%);
  }
  50% {
    transform: scale(1.1);
    background: linear-gradient(45deg, #ef4444 0%, #dc2626 100%);
  }
  75% {
    transform: scale(1.05);
    background: linear-gradient(45deg, #8b5cf6 0%, #7c3aed 100%);
  }
  100% {
    transform: scale(1);
    background: linear-gradient(45deg, #22c55e 0%, #16a34a 100%);
  }
}

/* Disabled button styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

button:disabled:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}
