// Game Variables
const DICE_MIN = 1;
const DICE_MAX = 6;

// DOM Elements
const nextBatterBtn = document.getElementById("nextBatterBtn");
const dice1 = document.getElementById("dice1");
const dice2 = document.getElementById("dice2");
const diceScore = document.getElementById("diceScore");
const scoreLabel = document.getElementById("scoreLabel");
const myTbl = document.getElementById("myTbl");

// Game State
let gameStarted = false;
let currentInning = 1;
let currentTeam = "HOME"; // HOME or GUEST
let strikes = 0;
let balls = 0;
let outs = 0;
let homeScore = 0;
let guestScore = 0;
let homeHits = 0;
let guestHits = 0;
let homeErrors = 0;
let guestErrors = 0;

// Outcome Table information
const diceOutcome = {
  11: `Double`,
  12: `Single`,
  13: `Single`,
  14: `Single – Runners advance two bases`,
  15: `Base on error`,
  16: `Base on balls`,
  22: `Strike`,
  23: `Strike`,
  24: `Strike – If runner on 1st base, choose to roll a die to steal. Even safe, odd out.`,
  25: `Strike`,
  26: `Foul out`,
  33: `Out at 1st`,
  34: `Out at 1st`,
  35: `Out at 1st`,
  36: `Out at 1st – Runners advance one base`,
  44: `Fly out`,
  45: `Fly out`,
  46: `Fly out – Runners advance one base`,
  55: `Double play`,
  56: `Triple`,
  66: `HOME RUN!`,
};

function startGame() {
  gameStarted = true;
  document.getElementById("startGameBtn").hidden = true;
  document.getElementById("nextBatterBtn").removeAttribute("hidden");

  // Initialize dice display
  dice1.textContent = "?";
  dice2.textContent = "?";

  // Update scoreboard
  updateScoreboard();
}

// Roll 2 6-sided dice and process the outcome
function rollTheDice() {
  if (!gameStarted) return;

  // Add dice rolling animation
  animateDiceRoll();

  // Get dice rolls
  const diceRoll1 = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;
  const diceRoll2 = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;

  // Sort dice from lowest to highest
  const diceResultLow = Math.min(diceRoll1, diceRoll2);
  const diceResultHi = Math.max(diceRoll1, diceRoll2);

  // Delay to show animation
  setTimeout(() => {
    // Display results
    dice1.textContent = diceResultLow;
    dice2.textContent = diceResultHi;

    // Show outcome
    const outcomeKey = `${diceResultLow}${diceResultHi}`;
    const outcome = diceOutcome[outcomeKey];

    scoreLabel.innerText = outcome || "Unknown outcome";
    diceScore.removeAttribute("hidden");

    // Process the outcome for game state
    processOutcome(outcome);

    // Update scoreboard
    updateScoreboard();
  }, 1000);

  return `${diceResultLow}${diceResultHi}`;
}

// Animate dice rolling
function animateDiceRoll() {
  let rollCount = 0;
  const maxRolls = 10;

  const rollInterval = setInterval(() => {
    dice1.textContent = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;
    dice2.textContent = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;
    rollCount++;

    if (rollCount >= maxRolls) {
      clearInterval(rollInterval);
    }
  }, 100);
}

// Process the outcome and update game state
function processOutcome(outcome) {
  const outcomeType = getOutcomeType(outcome);

  switch (outcomeType) {
    case "strike":
      strikes++;
      if (strikes >= 3) {
        outs++;
        strikes = 0;
        balls = 0;
      }
      break;
    case "ball":
      balls++;
      if (balls >= 4) {
        // Walk - reset count
        strikes = 0;
        balls = 0;
      }
      break;
    case "out":
      outs++;
      strikes = 0;
      balls = 0;
      break;
    case "hit":
      // Reset count on hit and update stats
      strikes = 0;
      balls = 0;

      // Update hits and potentially score
      if (currentTeam === "HOME") {
        homeHits++;
        // Simple scoring logic - some hits score runs
        if (
          outcome.toLowerCase().includes("double") ||
          outcome.toLowerCase().includes("triple") ||
          outcome.toLowerCase().includes("home run")
        ) {
          homeScore++;
        }
      } else {
        guestHits++;
        if (
          outcome.toLowerCase().includes("double") ||
          outcome.toLowerCase().includes("triple") ||
          outcome.toLowerCase().includes("home run")
        ) {
          guestScore++;
        }
      }
      break;
    case "error":
      // Update error count
      if (currentTeam === "HOME") {
        guestErrors++; // Fielding team gets the error
      } else {
        homeErrors++;
      }
      strikes = 0;
      balls = 0;
      break;
  }

  // Check for inning change
  if (outs >= 3) {
    outs = 0;
    if (currentTeam === "HOME") {
      currentTeam = "GUEST";
    } else {
      currentTeam = "HOME";
      currentInning++;
    }
  }
}

// Determine outcome type for game logic
function getOutcomeType(outcome) {
  const lowerOutcome = outcome.toLowerCase();

  if (lowerOutcome.includes("strike") || lowerOutcome.includes("foul")) {
    return "strike";
  } else if (lowerOutcome.includes("ball")) {
    return "ball";
  } else if (lowerOutcome.includes("error")) {
    return "error";
  } else if (
    lowerOutcome.includes("out") ||
    lowerOutcome.includes("double play")
  ) {
    return "out";
  } else if (
    lowerOutcome.includes("single") ||
    lowerOutcome.includes("double") ||
    lowerOutcome.includes("triple") ||
    lowerOutcome.includes("home run")
  ) {
    return "hit";
  }

  return "other";
}

// Update scoreboard display
function updateScoreboard() {
  // Update scores and stats
  document.getElementById("HOME").textContent = `HOME: ${homeScore}`;
  document.getElementById("GUEST").textContent = `GUEST: ${guestScore}`;
  document.getElementById("inning").textContent = `Inning: ${currentInning}`;
  document.getElementById("strikes").textContent = `Strikes: ${strikes}`;
  document.getElementById("balls").textContent = `Balls: ${balls}`;
  document.getElementById("outs").textContent = `Outs: ${outs}`;
  document.getElementById("hits").textContent = `Hits: ${
    currentTeam === "HOME" ? homeHits : guestHits
  }`;
  document.getElementById("errors").textContent = `Errors: ${
    currentTeam === "HOME" ? homeErrors : guestErrors
  }`;

  // Highlight current team
  const homeElement = document.getElementById("HOME");
  const guestElement = document.getElementById("GUEST");

  if (currentTeam === "HOME") {
    homeElement.style.backgroundColor = "rgba(255, 255, 0, 0.3)";
    homeElement.style.fontWeight = "bold";
    guestElement.style.backgroundColor = "transparent";
    guestElement.style.fontWeight = "normal";
  } else {
    guestElement.style.backgroundColor = "rgba(255, 255, 0, 0.3)";
    guestElement.style.fontWeight = "bold";
    homeElement.style.backgroundColor = "transparent";
    homeElement.style.fontWeight = "normal";
  }

  // Add team indicator
  const teamIndicator = currentTeam === "HOME" ? "🏠" : "👥";
  document.getElementById(
    "inning"
  ).textContent = `${teamIndicator} Inning: ${currentInning}`;
}

// Build the outcome reference table
function buildOutcomeTable() {
  // Create table header
  const headerRow = document.createElement("tr");

  const headers = ["Dice 1", "Dice 2", "Baseball Outcome"];
  headers.forEach((headerText) => {
    const th = document.createElement("th");
    th.textContent = headerText;
    headerRow.appendChild(th);
  });

  myTbl.appendChild(headerRow);

  // Create table rows for each outcome
  for (const outcomeKey in diceOutcome) {
    const tableRow = document.createElement("tr");

    // First die
    const tableData1 = document.createElement("td");
    tableData1.textContent = outcomeKey.charAt(0);
    tableData1.className = "tblDice";

    // Second die
    const tableData2 = document.createElement("td");
    tableData2.textContent = outcomeKey.charAt(1);
    tableData2.className = "tblDice";

    // Outcome
    const tableData3 = document.createElement("td");
    tableData3.textContent = diceOutcome[outcomeKey];
    tableData3.className = "tblOutcome";

    tableRow.appendChild(tableData1);
    tableRow.appendChild(tableData2);
    tableRow.appendChild(tableData3);
    myTbl.appendChild(tableRow);
  }
}

// Add sound effects (simple beep sounds using Web Audio API)
function playSound(frequency, duration) {
  try {
    const audioContext = new (window.AudioContext ||
      window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = "sine";

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      audioContext.currentTime + duration
    );

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
  } catch (e) {
    // Silently fail if audio context is not supported
  }
}

// Add celebration animation for home runs
function celebrateHomeRun() {
  const field = document.getElementById("field");
  field.style.animation = "celebrate 2s ease-in-out";

  // Reset animation
  setTimeout(() => {
    field.style.animation = "";
  }, 2000);

  // Play celebration sound
  playSound(523, 0.2); // C note
  setTimeout(() => playSound(659, 0.2), 200); // E note
  setTimeout(() => playSound(784, 0.4), 400); // G note
}

// Enhanced outcome processing with sound effects
function processOutcomeWithEffects(outcome) {
  processOutcome(outcome);

  // Add sound effects based on outcome
  const lowerOutcome = outcome.toLowerCase();

  if (lowerOutcome.includes("home run")) {
    celebrateHomeRun();
  } else if (lowerOutcome.includes("strike")) {
    playSound(200, 0.1); // Low strike sound
  } else if (
    lowerOutcome.includes("hit") ||
    lowerOutcome.includes("single") ||
    lowerOutcome.includes("double")
  ) {
    playSound(400, 0.2); // Hit sound
  } else if (lowerOutcome.includes("out")) {
    playSound(150, 0.3); // Out sound
  }
}

// Update the rollTheDice function to use enhanced processing
function rollTheDiceEnhanced() {
  if (!gameStarted) return;

  // Disable button during roll
  nextBatterBtn.disabled = true;
  nextBatterBtn.textContent = "🎲 Rolling...";

  // Add dice rolling animation
  animateDiceRoll();

  // Get dice rolls
  const diceRoll1 = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;
  const diceRoll2 = Math.floor(Math.random() * DICE_MAX) + DICE_MIN;

  // Sort dice from lowest to highest
  const diceResultLow = Math.min(diceRoll1, diceRoll2);
  const diceResultHi = Math.max(diceRoll1, diceRoll2);

  // Delay to show animation
  setTimeout(() => {
    // Display results
    dice1.textContent = diceResultLow;
    dice2.textContent = diceResultHi;

    // Show outcome
    const outcomeKey = `${diceResultLow}${diceResultHi}`;
    const outcome = diceOutcome[outcomeKey];

    scoreLabel.innerText = outcome || "Unknown outcome";
    diceScore.removeAttribute("hidden");

    // Process the outcome with effects
    processOutcomeWithEffects(outcome);

    // Update scoreboard
    updateScoreboard();

    // Re-enable button
    nextBatterBtn.disabled = false;
    nextBatterBtn.textContent = "🎲 Roll Dice";

    // Check for game end (simple 9 inning game)
    if (currentInning > 9) {
      endGame();
    }
  }, 1000);

  return `${diceResultLow}${diceResultHi}`;
}

// Game end function
function endGame() {
  nextBatterBtn.disabled = true;
  nextBatterBtn.textContent = "Game Over!";

  const winner =
    homeScore > guestScore ? "HOME" : guestScore > homeScore ? "GUEST" : "TIE";
  const message =
    winner === "TIE" ? "It's a tie game!" : `${winner} team wins!`;

  scoreLabel.innerHTML = `<strong>FINAL SCORE</strong><br/>HOME: ${homeScore} - GUEST: ${guestScore}<br/>${message}`;

  // Play end game sound
  if (winner !== "TIE") {
    celebrateHomeRun();
  }
}

// Initialize the game when page loads
document.addEventListener("DOMContentLoaded", function () {
  buildOutcomeTable();

  // Set initial dice display
  dice1.textContent = "?";
  dice2.textContent = "?";

  // Replace the rollTheDice function with enhanced version
  window.rollTheDice = rollTheDiceEnhanced;
});
