// Variables
let outcomeChartHeading1 = "Dice1";
let outcomeChartHeading2 = "Dice2";
let outcomeChartHeading3 = "Dice Outcome Chart";
const nextBatterBtn = document.getElementById("nextBatterBtn");
const dice1 = document.getElementById("dice1");
const dice2 = document.getElementById("dice2");
let diceScore = document.getElementById("diceScore");
let scoreLabel = document.getElementById("scoreLabel");
const min = 1;
const max = 6;

// Outcome Table information
const diceOutcome = {
  11: `Double`,
  12: `Single`,
  13: `Single`,
  14: `Single – Runners advance two bases`,
  15: `Base on error`,
  16: `Base on balls`,
  22: `Strike`,
  23: `Strike`,
  24: `Strike – If runner on 1st base, choose to roll a die to steal. Even safe, odd out.`,
  25: `Strike`,
  26: `Foul out`,
  33: `Out at 1st`,
  34: `Out at 1st`,
  35: `Out at 1st`,
  36: `Out at 1st – Runners advance one base`,
  44: `Fly out`,
  45: `Fly out`,
  46: `Fly out – Runners advance one base`,
  55: `Double play`,
  56: `Triple`,
  66: `HOME RUN!`,
};

function startGame() {
  document.getElementById("startGameBtn").hidden = true;
  document.getElementById("nextBatterBtn").removeAttribute("hidden");
}

// Roll 2 6 sided dice for a score
function rollTheDice() {
  // unhide dice outcome
  document.getElementById("diceScore").removeAttribute("hidden");

  // get dice Roll for 1 & 2
  let diceRoll1 = Math.floor(Math.random() * max) + min;
  let diceRoll2 = Math.floor(Math.random() * max) + min;

  // sort dice from lowest to highest
  diceResultLow = Math.min(diceRoll1, diceRoll2);
  diceResultHi = Math.max(diceRoll1, diceRoll2);

  // put the results to HTML
  dice1.textContent = diceResultLow;
  dice2.textContent = diceResultHi;

  // Output dice rolls for testing
  // console.log(`dice rolls are: ${diceResultLow} ${diceResultHi}`);

  scoreLabel.innerText = diceOutcome[`${diceResultLow}${diceResultHi}`];
  diceScore.appendChild(scoreLabel);

  return `${diceResultLow}${diceResultHi}`;
}

// Build the Table:
// Heading 1
myTbl = document.getElementById("myTbl");
let tableRow = document.createElement("tr");
let tableHead = document.createElement("th");
tableHead.innerHTML = `${outcomeChartHeading1}`;
tableRow.appendChild(tableHead);
myTbl.appendChild(tableRow);

// Heading 2
myTbl = document.getElementById("myTbl");
tableHead = document.createElement("th");
tableHead.innerHTML = `${outcomeChartHeading2}`;
tableRow.appendChild(tableHead);
myTbl.appendChild(tableRow);

// Heading 3
myTbl = document.getElementById("myTbl");
tableHead = document.createElement("th");
tableHead.innerHTML = `${outcomeChartHeading3}`;
tableRow.appendChild(tableHead);
myTbl.appendChild(tableRow);

// go through each line of the diceOutcome object and build table HTML
for (const i in diceOutcome) {
  result = `${i} = ${diceOutcome[i]}\n`;
  let tableRow = document.createElement("tr");
  let tableData1 = document.createElement("td");
  tableData1.innerHTML = `${i.charAt(0)}`;
  tableData1.setAttribute("class", "tblDice tblOutcome");
  let tableData2 = document.createElement("td");
  tableData2.innerHTML = `${i.charAt(1)}`;
  tableData2.setAttribute("class", "tblDice tblOutcome");
  let tableData3 = document.createElement("td");
  tableData3.setAttribute("class", "tblOutcome");
  tableData3.innerHTML = `${diceOutcome[i]}`;
  tableRow.appendChild(tableData1);
  tableRow.appendChild(tableData2);
  tableRow.appendChild(tableData3);
  myTbl.appendChild(tableRow);
}
