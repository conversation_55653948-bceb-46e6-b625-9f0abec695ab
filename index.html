<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Baseball</title>
    <link rel="stylesheet" href="style.css" />
    <script src="index.js" defer></script>
  </head>
  <body>
    <div id="wrapper-div">
      <div id="logo-div">Baseball Logo</div>
      <div id="nav-div"></div>
      <div id="header-banner-div">
        <div id="scoreboard-container">
          <div id="scoreboard">
            <div id="HOME" class="scoreboard-left">HOME:</div>
            <div id="GUEST" class="scoreboard-left">GUEST:</div>
            <div id="inning" class="scoreboard-left">Inning</div>
            <div id="strikes" class="scoreboard-left">Strike</div>
            <div id="balls" class="scoreboard-left">Ball</div>
            <div id="outs" class="scoreboard-left">Outs</div>
            <div id="hits" class="scoreboard-left">Hits</div>
            <div id="errors" class="scoreboard-left">errors</div>
          </div>
          <div id="field" class="scoreboard-right">FIELD</div>
        </div>
        <div class="dice-container" class="clearfix">
          <div id="dice1" class="dice">Dice 1</div>
          <div id="dice2" class="dice">Dice 2</div>
        </div>
        <div id="diceScore" hidden>
          Dice Outcome is:<br /><label id="scoreLabel">Label</label>
        </div>
      </div>
      <div id="main-div" class="clearfix">
        <div id="sidebar-div">
          <button id="startGameBtn" class="startGameBtn" onclick="startGame()">
            Start Game
          </button>
          <button
            hidden
            id="nextBatterBtn"
            class="nextBatterBtn"
            onclick="rollTheDice()"
          >
            Next Batter</button
          ><br /><br />
        </div>
        <div id="bodyarea-div">
          <table id="myTbl" class="myTbl"></table>
        </div>
      </div>
      <div id="footer-div"></div>
    </div>
  </body>
</html>
